{"name": "tms_app", "version": "1.0.0", "main": "expo-router/entry", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "lint": "eslint .", "test": "jest"}, "dependencies": {"@react-native-picker/picker": "^2.11.0", "autoprefixer": "^10.4.21", "expo": "~53.0.9", "expo-constants": "~17.1.6", "expo-linear-gradient": "^14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-status-bar": "~2.2.3", "nativewind": "^4.1.23", "postcss": "^8.5.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-drawer-layout": "^4.1.8", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-webview": "13.13.5", "tailwindcss": "^3.3.2", "expo-blur": "~14.1.4"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "babel-preset-expo": "~13.0.0", "typescript": "~5.8.3"}, "private": true}