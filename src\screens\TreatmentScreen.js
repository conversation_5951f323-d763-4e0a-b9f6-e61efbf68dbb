import React, { useState } from "react";
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, Image, Linking, Pressable } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { LinearGradient } from 'expo-linear-gradient';

// Import components
import AppBar from "../components/AppBar";

// Import constants
import Colors from "../constants/Colors";
import Fonts from "../constants/Fonts";
import Layout from "../constants/Layout";

const tmsCards = [
  {
    text: "You Or Your Loved One Has...\nSymptoms of depression or OCD that have not improved after attempts with medication.",
  },
  {
    text: "You Or Your Loved One Has...\nOutweighing the benefit of medication. With little to no side effects most TMS patients experience relief and show benefit in as little as 2-4 weeks.",
  },
  {
    text: "Your Quality Of Life Is...\nSuffering because of your depression, OCD, or other diagnosis.",
  },
];

const faqItems = [
  { question: "Is TMS Therapy Painful?", answer: "No, TMS therapy is generally not painful. Most patients describe a tapping or knocking sensation on their scalp during treatment. Some may experience mild discomfort that typically subsides after the first few sessions." },
  { question: "How Long Does a TMS Treatment Session Last?", answer: "A typical TMS treatment session lasts about 20-40 minutes. The full course of treatment usually involves 5 sessions per week for 4-6 weeks, totaling 20-30 sessions." },
  { question: "How Many TMS Sessions Will I Need?", answer: "Most patients undergo 20-30 sessions over 4-6 weeks. Your doctor will recommend a plan tailored to your needs." },
  { question: "Are There Any Side Effects of TMS Therapy?", answer: "TMS is well-tolerated. The most common side effect is mild scalp discomfort or headache, which usually resolves after a few sessions." },
  { question: "Can TMS Therapy Be Combined with Medication?", answer: "Yes, TMS therapy can be used alongside medication. Many patients continue their current medications during TMS treatment. Your doctor will provide guidance on your specific treatment plan." },
  { question: "How Soon Will I Notice Results from TMS Therapy?", answer: "Many patients notice improvement after 2-3 weeks of treatment, with benefits continuing to accrue throughout the course of therapy." },
];

/**
 * Treatment Screen Component
 */
export default function TreatmentScreen() {
  const router = useRouter();
  const [expandedFaq, setExpandedFaq] = useState(null);

  const handleContact = () => router.push("/contact");

  const toggleFaq = (index) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  return (
    <AppBar>
      <ScrollView style={{ flex: 1, backgroundColor: Colors.background }} contentContainerStyle={{ paddingBottom: 70 }}>
        {/* Hero Section */}
        <View style={styles.heroSection}>
          <Image source={require("../../assets/video-thumbnail.png")} style={styles.heroImage} />
          <View style={styles.heroOverlay}>
            <Text style={styles.heroTitle}>Treatment</Text>
          </View>
        </View>

        {/* Section matching reference image */}
        <View style={styles.featureSection}>
          <Image source={require("../../assets/treatment.png")} style={styles.featureImage} />
          <View style={styles.treatmentHeaderRow}>
            <View style={styles.treatmentHeaderLine} />
            <Text style={styles.treatmentHeading}>OUR TEAM</Text>
            <View style={styles.treatmentHeaderLine} />
          </View>
          <Text style={styles.featureTitle}><Text style={styles.featureTitleBlue}>Providing The Ultimate </Text><Text style={styles.featureTitleBlack}>TMS Treatment</Text></Text>
          <Text style={styles.featureDesc}>
            Where other treatments have failed, we offer an effective solution for treatment resistant Depression, OCD, and other mental health inhibitors, without the use of medication, side effects, or invasive procedures — using FDA approved, state-of-the-art technology that only Brain Ultimate provides.
          </Text>
          <TouchableOpacity style={styles.featureButton} onPress={handleContact}>
            <Text style={styles.featureButtonText}>LEARN MORE</Text>
            <Ionicons name="arrow-forward" size={18} color={Colors.white} style={styles.featureButtonIcon} />
          </TouchableOpacity>
        </View>

        {/* TMS Therapy Is For You If... Section */}
        <View style={styles.tmsIfSection}>
          <Text style={styles.tmsIfTitle}>
            <Text style={styles.tmsIfTitleBlue}>TMS Therapy</Text>
            <Text style={styles.tmsIfTitleBlack}> Is For You If...</Text>
          </Text>
          <View style={styles.tmsCardsRow}>
            <LinearGradient colors={["#e3ecef", "#2c5264"]} style={styles.tmsCard} start={{x:0.5, y:0}} end={{x:0.5, y:1}}>
              <View style={styles.tmsCardLogoCircle}>
                <Image source={require("../../assets/tms-logo.png")} style={styles.tmsCardLogo} />
              </View>
              <Text style={styles.tmsCardTitle}>You Or Your Loved One Has...</Text>
              <Text style={styles.tmsCardText}>Symptoms of depression or OCD that have not improved after attempts with medication.</Text>
            </LinearGradient>
            <LinearGradient colors={["#e3ecef", "#2c5264"]} style={styles.tmsCard} start={{x:0.5, y:0}} end={{x:0.5, y:1}}>
              <View style={styles.tmsCardLogoCircle}>
                <Image source={require("../../assets/tms-logo.png")} style={styles.tmsCardLogo} />
              </View>
              <Text style={styles.tmsCardTitle}>You Or Your Loved One Has...</Text>
              <Text style={styles.tmsCardText}>Outweighing the benefit of medications. With little to no side effects most TMS patients experience relief and show benefit in as little as 2–4 weeks.</Text>
            </LinearGradient>
            <LinearGradient colors={["#e3ecef", "#2c5264"]} style={styles.tmsCard} start={{x:0.5, y:0}} end={{x:0.5, y:1}}>
              <View style={styles.tmsCardLogoCircle}>
                <Image source={require("../../assets/tms-logo.png")} style={styles.tmsCardLogo} />
              </View>
              <Text style={styles.tmsCardTitle}>Your Quality Of Life Is...</Text>
              <Text style={styles.tmsCardText}>Suffering because of your depression, OCD, or other diagnosis.</Text>
            </LinearGradient>
          </View>
        </View>

        {/* FAQ Section */}
        <View style={styles.faqSection}>
          <Text style={styles.faqTitle}>Frequently Asked Questions</Text>
          {faqItems.map((item, index) => (
            <Pressable
              key={index}
              style={[styles.faqItem, expandedFaq === index && styles.faqItemExpanded]}
              onPress={() => setExpandedFaq(expandedFaq === index ? null : index)}
            >
              <View style={styles.faqQuestionRow}>
                <Text style={styles.faqQuestionText}>{item.question}</Text>
                <Ionicons name={expandedFaq === index ? "chevron-up" : "chevron-down"} size={28} color={Colors.primary} />
              </View>
              {expandedFaq === index && (
                <Text style={styles.faqAnswerText}>{item.answer}</Text>
              )}
            </Pressable>
          ))}
        </View>

        {/* Take Control Section (from About) */}
        <View style={styles.takeControlSectionCustom}>
          <Text style={styles.takeControlTitleCustom}>Take Control Of</Text>
          <Text style={styles.takeControlSubtitleCustom}>Your Depression</Text>
          <Text style={styles.takeControlTextCustom}>
            Reclaim your life with TMS therapy. Schedule a consultation with our experienced team at TMS of Emerald Coast to discuss your treatment options and start your journey to recovery.
          </Text>
          <TouchableOpacity style={styles.takeControlButtonCustom} onPress={handleContact}>
            <Text style={styles.takeControlButtonTextCustom}>CONTACT US</Text>
            <Ionicons name="arrow-forward" size={18} color={Colors.primary} style={styles.takeControlButtonIconCustom} />
          </TouchableOpacity>
          <View style={styles.takeControlImageWrapper}>
            <Image source={require("../../assets/depression.png")} style={styles.takeControlImageCustom} resizeMode="cover" />
          </View>
        </View>


      </ScrollView>
    </AppBar>
  );
}

const styles = StyleSheet.create({
  heroSection: {
    height: 180,
    position: "relative",
  },
  heroImage: {
    width: "100%",
    height: "100%",
  },
  heroOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(44,82,100,0.25)",
    justifyContent: "flex-end",
    alignItems: "flex-start",
    paddingLeft: Layout.spacing.xlarge,
    paddingBottom: Layout.spacing.large,
  },
  heroTitle: {
    fontSize: Fonts.sizes.xxlarge,
    fontWeight: Fonts.weights.bold,
    color: Colors.white,
    letterSpacing: 1,
    textAlign: "left",
    marginBottom: 12,
  },
  curveTransition: {
    width: "100%",
    height: 64,
    backgroundColor: Colors.background,
    borderTopLeftRadius: 60,
    borderTopRightRadius: 60,
    marginTop: -20,
    marginBottom: 0,
    zIndex: 1,
  },
  featureSection: {
    backgroundColor: Colors.background,
    borderRadius: 24,
    marginHorizontal: 16,
    marginTop: 40,
    paddingBottom: 24,
    alignItems: 'center',
    shadowColor: Colors.black,
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  featureImage: {
    width: '100%',
    height: 180,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginBottom: 16,
    resizeMode: 'cover',
  },
  treatmentHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  treatmentHeaderLine: {
    flex: 1,
    height: 2,
    backgroundColor: Colors.primary,
    opacity: 0.4,
    marginHorizontal: 8,
    borderRadius: 1,
  },
  treatmentHeading: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
    fontSize: Fonts.sizes.medium,
    letterSpacing: 2,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  featureTitle: {
    fontSize: 22,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 10,
    marginTop: 0,
  },
  featureTitleBlue: {
    color: Colors.primary,
    fontWeight: '700',
  },
  featureTitleBlack: {
    color: Colors.text,
    fontWeight: '700',
  },
  featureDesc: {
    color: Colors.text,
    fontSize: Fonts.sizes.regular,
    textAlign: 'center',
    marginBottom: 18,
    marginTop: 0,
    lineHeight: 20,
    paddingHorizontal: 8,
  },
  featureButton: {
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.medium,
    paddingVertical: 10,
    paddingHorizontal: 24,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
  },
  featureButtonText: {
    color: Colors.white,
    fontWeight: Fonts.weights.bold,
    fontSize: Fonts.sizes.regular,
    letterSpacing: 1,
    marginRight: 8,
  },
  featureButtonIcon: {
    marginLeft: 0,
  },
  tmsIfSection: {
    marginTop: 32,
    marginBottom: 16,
    paddingHorizontal: 0,
  },
  tmsIfTitle: {
    fontSize: 20,
    fontWeight: '700',
    textAlign: 'left',
    marginBottom: 18,
    marginLeft: 8,
  },
  tmsIfTitleBlue: {
    color: Colors.primary,
    fontWeight: '700',
  },
  tmsIfTitleBlack: {
    color: Colors.text,
    fontWeight: '700',
  },
  tmsCardsRow: {
    gap: 18,
    alignItems: 'center',
  },
  tmsCard: {
    borderRadius: 16,
    alignItems: 'center',
    padding: 18,
    width: '96%',
    maxWidth: 400,
    alignSelf: 'center',
    marginBottom: 0,
    elevation: 2,
    shadowColor: Colors.black,
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    overflow: 'hidden',
  },
  tmsCardLogo: {
    width: 90,
    height: 90,
    marginBottom: 10,
    resizeMode: 'contain',
  },
  tmsCardTitle: {
    color: Colors.text,
    fontSize: 18,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 6,
  },
  tmsCardText: {
    color: Colors.white,
    fontSize: 15,
    textAlign: 'center',
    lineHeight: 20,
    fontWeight: '400',
  },
  tmsCardLogoCircle: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 10,
  },
  faqSection: {
    padding: 40,
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.large,
    marginHorizontal: 16,
    marginTop: 32,
    marginBottom: 32,
  },
  faqTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.white,
    marginBottom: 30,
    textAlign: 'center',
  },
  faqItem: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 5,
    marginBottom: 15,
    overflow: 'hidden',
    backgroundColor: Colors.lightGray,
  },
  faqItemExpanded: {
    borderColor: Colors.primary,
  },
  faqQuestionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 8,
    paddingHorizontal: 15,
    backgroundColor: Colors.lightGray,
  },
  faqQuestionText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.primary,
    flex: 1,
  },
  faqAnswerText: {
    fontSize: 15,
    color: Colors.darkGray,
    padding: 15,
    lineHeight: 20,
  },
  takeControlSectionCustom: {
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.xxlarge,
    padding: 24,
    marginHorizontal: 16,
    marginBottom: 32,
    alignItems: 'center',
  },
  takeControlTitleCustom: {
    color: Colors.white,
    fontSize: 24,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 0,
  },
  takeControlSubtitleCustom: {
    color: Colors.black,
    fontSize: 24,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 18,
    marginTop: 0,
  },
  takeControlTextCustom: {
    color: Colors.white,
    fontSize: 15,
    textAlign: 'center',
    marginBottom: 24,
    marginHorizontal: 8,
  },
  takeControlButtonCustom: {
    backgroundColor: Colors.white,
    borderRadius: Layout.borderRadius.xxlarge,
    paddingVertical: 12,
    paddingHorizontal: 32,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 24,
    marginTop: 0,
  },
  takeControlButtonTextCustom: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
    fontSize: 15,
    marginRight: 8,
  },
  takeControlButtonIconCustom: {
    marginLeft: 0,
  },
  takeControlImageWrapper: {
    width: '100%',
    backgroundColor: '#e5e7eb',
    borderRadius: Layout.borderRadius.large,
    alignItems: 'center',
    marginTop: 0,
  },
  takeControlImageCustom: {
    width: '100%',
    height: 110,
    borderRadius: Layout.borderRadius.large,
  },

});
