{"expo": {"scheme": "tmsapp", "name": "tms_app", "slug": "tms_app", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.ramsha_malik.tms_app"}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router"], "extra": {"router": {}, "eas": {"projectId": "aec7ddf4-9474-4131-9a9a-4b9309bf4176"}}}}