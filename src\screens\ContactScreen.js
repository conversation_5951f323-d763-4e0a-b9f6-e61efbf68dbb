import { useState, useRef } from "react";
import {
  StyleSheet,
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  TextInput,
  Linking,
  Alert,
  Image,
  ImageBackground,
  Platform,
  ActivityIndicator,
  Animated,
} from "react-native";
import { Ionicons, FontAwesome } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import { Picker } from '@react-native-picker/picker';

// Import components
import AppBar from "../components/AppBar";

// Import context
import { useScrollViewPadding } from "../context/BottomNavContext";

// Import constants
import Colors from "../constants/Colors";
import Fonts from "../constants/Fonts";
import Layout from "../constants/Layout";

// Mapbox configuration
const MAPBOX_TOKEN = "pk.eyJ1IjoicmFtc2hhbWFsaWsiLCJhIjoiY2x2MnZqZ2R0MGRydzJpcGZoYW0wMW5pdSJ9._VUx5z0bwIRtFhZHF3_cFQ";

// Mapbox style options
const MAPBOX_STYLES = {
  streets: "streets-v11",
  outdoors: "outdoors-v11",
  light: "light-v10",
  dark: "dark-v10",
  satellite: "satellite-v9",
  satelliteStreets: "satellite-streets-v11"
};

// Location coordinates
const OFFICE_LOCATION = {
  longitude: -86.6218,
  latitude: 30.4205,
  address: "403 Hollywood Blvd NW Suite 104A Fort Walton Beach, FL 32548",
  zoom: 15,
  markerColor: "f74e4e" // Hex color for the map marker (red, without #)
};

/**
 * Contact Screen Component
 */
export default function ContactScreen() {
  const router = useRouter();
  const scrollViewPadding = useScrollViewPadding();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [phone, setPhone] = useState("");
  const [message, setMessage] = useState("");
  const [date, setDate] = useState("");
  const [consultation, setConsultation] = useState("");
  const [mapLoading, setMapLoading] = useState(true);
  const [mapStyle, setMapStyle] = useState(MAPBOX_STYLES.satelliteStreets);

  const handleCall = () => {
    Linking.openURL("tel:************");
  };

  // Function to open location in maps app
  const openInMaps = () => {
    const scheme = Platform.select({ ios: 'maps:0,0?q=', android: 'geo:0,0?q=' });
    const latLng = `${OFFICE_LOCATION.latitude},${OFFICE_LOCATION.longitude}`;
    const label = encodeURIComponent(OFFICE_LOCATION.address);
    const url = Platform.select({
      ios: `${scheme}${label}@${latLng}`,
      android: `${scheme}${latLng}(${label})`
    });

    Linking.openURL(url);
  };

  // Function to cycle through map styles
  const cycleMapStyle = () => {
    setMapLoading(true);
    const styles = Object.values(MAPBOX_STYLES);
    const currentIndex = styles.indexOf(mapStyle);
    const nextIndex = (currentIndex + 1) % styles.length;
    setMapStyle(styles[nextIndex]);
  };

  const handleEmail = () => {
    Linking.openURL("mailto:<EMAIL>");
  };

  const handleSubmit = () => {
    if (!name || !email || !message) {
      Alert.alert("Missing Information", "Please fill in all required fields.");
      return;
    }

    // In a real app, you would send this data to your backend
    console.log({ name, email, phone, message });
    Alert.alert("Message Sent", "Thank you for contacting us. We will get back to you soon!", [
      {
        text: "OK",
        onPress: () => {
          setName("");
          setEmail("");
          setPhone("");
          setMessage("");
        },
      },
    ]);
  };

  return (
    <AppBar>
      <SafeAreaView style={styles.container}>
        <ScrollView
          style={styles.content}
          contentContainerStyle={scrollViewPadding}
        >
          {/* Hero Section */}
          <View style={styles.heroSection}>
            <Image source={require("../../assets/contact-hero.jpg")} style={styles.heroImage} />
            <View style={styles.heroOverlay}>
              <Text style={styles.heroTitle}>Contact</Text>
            </View>
              </View>

          {/* Contact Info Card Section */}
          <View style={styles.contactCardWrapper}>
            <ImageBackground source={require("../../assets/contact.jpg")} style={styles.contactCardBg} imageStyle={styles.contactCardBgImg}>
              <View style={styles.contactCardOverlay}>
                <View style={styles.supportContainer}>
                  <View style={styles.supportLine} />
                  <Text style={styles.contactCardTitle}>GET SUPPORT</Text>
                </View>
                <Text style={styles.contactCardSubtitle}>Reach Us By Contact Information.</Text>
                <Text style={styles.contactCardDesc}>Connect with our dedicated care team for personalized support and healing.</Text>
                <View style={styles.contactCardInfoRow}>
                  <Ionicons name="mail" size={22} color="#fff" style={styles.contactCardIcon} />
                  <Text style={styles.contactCardInfoText}><EMAIL></Text>
              </View>
                <View style={styles.contactCardInfoRow}>
                  <Ionicons name="call" size={22} color="#fff" style={styles.contactCardIcon} />
                  <Text style={styles.contactCardInfoText}>************</Text>
                </View>
                <View style={styles.contactCardInfoRow}>
                  <Ionicons name="print" size={22} color="#fff" style={styles.contactCardIcon} />
                  <Text style={styles.contactCardInfoText}>Fax: 850 750-4712</Text>
                </View>
                <View style={styles.contactCardInfoRow}>
                  <Ionicons name="location" size={22} color="#fff" style={styles.contactCardIcon} />
                  <Text style={styles.contactCardInfoText}>{OFFICE_LOCATION.address}</Text>
                </View>
                <View style={styles.cardDivider} />
                <View style={styles.socialLinksContainer}>
                  <TouchableOpacity style={styles.socialIconButton} onPress={() => Linking.openURL('https://www.facebook.com/profile.php?id=61568383621462&mibextid=LQQJ4di&rdid=NQbuAVyeIjACv0jp&share_url=https%3A%2F%2Fwww.facebook.com%2Fshare%2FaAL7QjSekUpwb3oP%2F%3Fmibextid%3DLQQJ4di#')}>
                    <FontAwesome name="facebook" size={22} color="#fff" />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.socialIconButton} onPress={() => Linking.openURL('https://www.linkedin.com/company/tms-of-emerald-coast-llc/')}>
                    <FontAwesome name="linkedin" size={22} color="#fff" />
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.socialIconButton} onPress={() => Linking.openURL('https://www.instagram.com/tmsemeraldcoast/?igsh=MWk1dHd1cmFwbWN6bg%3D%3D#')}>
                    <FontAwesome name="instagram" size={22} color="#fff" />
                  </TouchableOpacity>
                </View>
              </View>
            </ImageBackground>
          </View>

          <View style={styles.contentContainer}>
          <View style={styles.formContainer}>
              <Text style={styles.formTitle}>Send Us Message</Text>
              <Text style={styles.formSubtitle}>Reach Out for Immediate Support</Text>

            <TextInput
              style={styles.input}
                placeholder="Your Name"
              value={name}
              onChangeText={setName}
              placeholderTextColor="#666"
            />

            <TextInput
              style={styles.input}
                placeholder="Date-Month-Year"
                value={date}
                onChangeText={setDate}
                placeholderTextColor="#666"
              />

              <TextInput
                style={styles.input}
                placeholder="Your Email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              placeholderTextColor="#666"
            />

              <View style={styles.pickerWrapper}>
                <Picker
                  selectedValue={consultation}
                  onValueChange={setConsultation}
                  style={styles.picker}
                  dropdownIconColor="#666"
                >
                  <Picker.Item label="Consultation" value="" color="#666" />
                  <Picker.Item label="General Inquiry" value="general" />
                  <Picker.Item label="TMS Therapy" value="tms" />
                  <Picker.Item label="Insurance" value="insurance" />
                  <Picker.Item label="Other" value="other" />
                </Picker>
              </View>

            <TextInput
              style={[styles.input, styles.textArea]}
                placeholder="Message"
              value={message}
              onChangeText={setMessage}
              multiline
              numberOfLines={5}
              placeholderTextColor="#666"
            />

              <TouchableOpacity style={styles.contactButton} onPress={handleSubmit}>
                <Text style={styles.contactButtonText}>CONTACT</Text>
              </TouchableOpacity>
            </View>

            {/* Map Section - Using Mapbox */}
            <View style={styles.mapCard}>
              <Text style={styles.mapTitle}>{OFFICE_LOCATION.address}</Text>
              <Text style={styles.mapStyleIndicator}>
                Map Style: {mapStyle.replace('-v11', '').replace('-v10', '').replace('-v9', '')}
              </Text>
              {/* Static Mapbox map with marker */}
              <TouchableOpacity
                onPress={openInMaps}
                activeOpacity={0.9}
                style={styles.mapImageContainer}
              >
                {mapLoading && (
                  <View style={styles.mapLoadingContainer}>
                    <ActivityIndicator size="large" color={Colors.primary} />
                    <Text style={styles.mapLoadingText}>Loading map...</Text>
                  </View>
                )}
                <Image
                  source={{
                    uri: `https://api.mapbox.com/styles/v1/mapbox/${mapStyle}/static/pin-s+${OFFICE_LOCATION.markerColor}(${OFFICE_LOCATION.longitude},${OFFICE_LOCATION.latitude})/${OFFICE_LOCATION.longitude},${OFFICE_LOCATION.latitude},${OFFICE_LOCATION.zoom},0/600x300?access_token=${MAPBOX_TOKEN}`
                  }}
                  style={styles.mapImage}
                  resizeMode="cover"
                  onLoadStart={() => setMapLoading(true)}
                  onLoad={() => setMapLoading(false)}
                  onError={() => setMapLoading(false)}
                />
              </TouchableOpacity>
              <View style={styles.mapButtonsContainer}>
                <TouchableOpacity
                  style={styles.mapStyleButton}
                  onPress={cycleMapStyle}
                >
                  <Ionicons name="map" size={16} color={Colors.white} />
                  <Text style={styles.mapButtonText}>Change Style</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.mapDirectionsButton}
                  onPress={openInMaps}
                >
                  <Text style={styles.mapDirectionsButtonText}>Get Directions</Text>
                  <Ionicons name="navigate" size={16} color={Colors.white} style={{ marginLeft: 6 }} />
                </TouchableOpacity>
              </View>
            </View>


          </View>
        </ScrollView>
      </SafeAreaView>
    </AppBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: Layout.spacing.large,
  },
  title: {
    fontSize: Fonts.sizes.xlarge,
    fontWeight: Fonts.weights.bold,
    color: Colors.primary,
    marginBottom: Layout.spacing.xxlarge,
    textAlign: "center",
  },
  contactInfo: {
    marginBottom: Layout.spacing.xxlarge,
  },
  contactItem: {
    flexDirection: "row",
    marginBottom: Layout.spacing.large,
  },
  iconContainer: {
    width: 50,
    height: 50,
    backgroundColor: Colors.lightGray,
    borderRadius: Layout.borderRadius.round,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.medium,
  },
  contactDetails: {
    justifyContent: "center",
  },
  contactLabel: {
    fontSize: Fonts.sizes.regular,
    fontWeight: Fonts.weights.bold,
    color: Colors.text,
  },
  contactValue: {
    fontSize: Fonts.sizes.medium,
    color: Colors.lightText,
    marginTop: 5,
  },
  link: {
    color: Colors.primary,
    textDecorationLine: "underline",
  },
  socialLinks: {
    marginBottom: Layout.spacing.xxlarge,
  },
  socialTitle: {
    fontSize: Fonts.sizes.large,
    fontWeight: Fonts.weights.bold,
    color: Colors.primary,
    marginBottom: Layout.spacing.medium,
  },
  socialIcons: {
    flexDirection: "row",
  },
  socialIcon: {
    width: 40,
    height: 40,
    backgroundColor: Colors.lightGray,
    borderRadius: Layout.borderRadius.round,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.medium,
  },
  formContainer: {
    backgroundColor: '#d3e1e1',
    padding: 20,
    borderRadius: 18,
    marginBottom: 32,
    borderWidth: 2,
    borderColor: '#b7c9c9',
  },
  formTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#2a5d6b',
    marginBottom: 2,
    textAlign: 'left',
  },
  formSubtitle: {
    fontSize: 15,
    color: '#222',
    marginBottom: 18,
    textAlign: 'left',
    fontWeight: '400',
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 0,
    borderRadius: 8,
    padding: 14,
    marginBottom: 14,
    fontSize: 15,
  },
  pickerWrapper: {
    backgroundColor: '#fff',
    borderWidth: 0,
    borderRadius: 8,
    marginBottom: 14,
    overflow: 'hidden',
    height: 56,
    justifyContent: 'center',
  },
  picker: {
    height: 56,
    color: '#222',
    width: '100%',
    paddingLeft: 8,
    paddingRight: 8,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  contactButton: {
    backgroundColor: '#2a5d6b',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
    width: '100%',
  },
  contactButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 1,
  },
  heroSection: {
    height: 220,
    position: "relative",
    marginBottom: Layout.spacing.large,
  },
  heroImage: {
    width: "100%",
    height: "100%",
  },
  heroOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(44,82,100,0.25)",
    justifyContent: "center",
    alignItems: "flex-start",
    paddingLeft: Layout.spacing.xlarge,
    paddingTop: Layout.spacing.xxlarge,
  },
  heroTitle: {
    fontSize: Fonts.sizes.xxlarge,
    fontWeight: Fonts.weights.bold,
    color: Colors.white,
    letterSpacing: 1,
    textAlign: "left",
  },
  contactCardWrapper: {
    marginHorizontal: 16,
    marginBottom: 32,
    borderRadius: 24,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: Colors.black,
    shadowOpacity: 0.12,
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
  },
  contactCardBg: {
    width: '100%',
    minHeight: 220,
    justifyContent: 'center',
  },
  contactCardBgImg: {
    resizeMode: 'cover',
  },
  contactCardOverlay: {
    backgroundColor: 'rgba(20,30,40,0.75)',
    padding: 24,
    borderRadius: 24,
  },
  supportContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  supportLine: {
    width: 40,
    height: 3,
    backgroundColor: '#4db3c9',
    marginRight: 12,
  },
  contactCardTitle: {
    color: '#4db3c9',
    fontSize: 16,
    fontWeight: 'bold',
    letterSpacing: 2,
    textTransform: 'uppercase',
  },
  contactCardSubtitle: {
    color: '#fff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  contactCardDesc: {
    color: '#fff',
    fontSize: 15,
    marginBottom: 18,
  },
  contactCardInfoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  contactCardIcon: {
    marginRight: 12,
  },
  contactCardInfoText: {
    color: '#fff',
    fontSize: 15,
    flex: 1,
    flexWrap: 'wrap',
  },
  mapCard: {
    backgroundColor: '#d3e1e1',
    borderRadius: 18,
    borderWidth: 2,
    borderColor: '#b7c9c9',
    padding: 12,
    marginHorizontal: 0,
    marginBottom: 32,
    alignItems: 'center',
  },
  mapTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#2a5d6b',
    marginBottom: 4,
    textAlign: 'center',
  },
  mapStyleIndicator: {
    fontSize: 12,
    color: '#666',
    marginBottom: 10,
    textAlign: 'center',
    fontStyle: 'italic',
    textTransform: 'capitalize',
  },
  mapImageContainer: {
    width: '100%',
    height: 180,
    borderRadius: 12,
    overflow: 'hidden',
  },
  mapImage: {
    width: '100%',
    height: '100%',
  },
  mapLoadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  mapLoadingText: {
    marginTop: 10,
    color: Colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  mapButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 12,
    width: '100%',
  },
  mapStyleButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginRight: 8,
    justifyContent: 'center',
  },
  mapDirectionsButton: {
    backgroundColor: Colors.primary,
    borderRadius: 8,
    paddingVertical: 10,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: 8,
    justifyContent: 'center',
  },
  mapButtonText: {
    color: Colors.white,
    fontWeight: 'bold',
    fontSize: 14,
    marginLeft: 6,
  },
  mapDirectionsButtonText: {
    color: Colors.white,
    fontWeight: 'bold',
    fontSize: 14,
  },
  socialLinksContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 20,
    gap: 20,
  },
  socialIconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(255,255,255,0.1)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  cardDivider: {
    height: 1,
    backgroundColor: 'rgba(255,255,255,0.2)',
    marginVertical: 20,
  },
});


