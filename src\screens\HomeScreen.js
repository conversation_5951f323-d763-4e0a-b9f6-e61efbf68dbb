import { useState, useEffect } from "react";
import {
  StyleSheet,
  View,
  Text,
  Image,
  ScrollView,
  TouchableOpacity,
  TextInput,
  Linking,
  Pressable,
  Keyboard,
  Dimensions,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";

// Import components
import AppBar from "../components/AppBar";

// Import constants
import Colors from "../constants/Colors";
import Fonts from "../constants/Fonts";
import Layout from "../constants/Layout";

// Import utils
import { validateEmail, validateDate } from "../utils/validation";

/**
 * Home Screen Component
 */
export default function HomeScreen() {
  const router = useRouter();
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [date, setDate] = useState("");
  const [consultationType, setConsultationType] = useState("Consultation");
  const [showConsultationOptions, setShowConsultationOptions] = useState(false);
  const [expandedFaq, setExpandedFaq] = useState(null);
  const [dimensions, setDimensions] = useState({ window: Layout.window });

  // Form validation state
  const [nameError, setNameError] = useState("");
  const [emailError, setEmailError] = useState("");
  const [dateError, setDateError] = useState("");
  const [consultationTypeError, setConsultationTypeError] = useState("");

  useEffect(() => {
    const subscription = Dimensions.addEventListener("change", ({ window }) => {
      setDimensions({ window });
    });
    return () => subscription?.remove();
  }, []);

  const isTablet = dimensions.window.width > 768;
  const isMobile = dimensions.window.width <= 480;
  const isSmallDevice = Layout.isSmallDevice;

  const handleCall = () => {
    Linking.openURL("tel:************");
  };

  const handleEmail = () => {
    Linking.openURL("mailto:<EMAIL>");
  };

  const validateForm = () => {
    let valid = true;
    if (!name.trim()) {
      setNameError("Name is required.");
      valid = false;
    } else {
      setNameError("");
    }
    if (!date.trim()) {
      setDateError("Date is required.");
      valid = false;
    } else if (!validateDate(date.trim())) {
      setDateError("Enter date as DD-MM-YYYY or DD/MM/YYYY.");
      valid = false;
    } else {
      setDateError("");
    }
    if (!email.trim()) {
      setEmailError("Email is required.");
      valid = false;
    } else if (!validateEmail(email.trim())) {
      setEmailError("Enter a valid email address.");
      valid = false;
    } else {
      setEmailError("");
    }
    if (!consultationType || consultationType === "") {
      setConsultationTypeError("Please select a consultation type.");
      valid = false;
    } else {
      setConsultationTypeError("");
    }
    return valid;
  };

  const handleContactSubmit = () => {
    if (!validateForm()) return;
    console.log({ name, email, date, consultationType });
    alert("Thank you for contacting us. We will get back to you soon!");
    setName("");
    setEmail("");
    setDate("");
    setConsultationType("Consultation");
  };

  const toggleFaq = (index) => {
    setExpandedFaq(expandedFaq === index ? null : index);
  };

  const faqItems = [
    {
      question: "Is TMS Therapy Painful?",
      answer:
        "No, TMS therapy is generally not painful. Most patients describe a tapping or knocking sensation on their scalp during treatment. Some may experience mild discomfort that typically subsides after the first few sessions.",
    },
    {
      question: "How Long Does TMS Treatment Session Last?",
      answer:
        "A typical TMS treatment session lasts about 20-40 minutes. The full course of treatment usually involves 5 sessions per week for 4-6 weeks, totaling 20-30 sessions.",
    },
    {
      question: "Will My Insurance Cover TMS?",
      answer:
        "Many insurance providers now cover TMS therapy for patients who have not responded to traditional depression treatments. Our staff will work with you to verify your coverage and explain any out-of-pocket costs.",
    },
    {
      question: "How Long Will The Effects of TMS Therapy Last?",
      answer:
        "The effects of TMS therapy can last for months to years. Some patients experience long-term relief after a single course of treatment, while others may benefit from occasional maintenance sessions.",
    },
    {
      question: "Can TMS Therapy Be Combined with Medication?",
      answer:
        "Yes, TMS therapy can be used alongside medication. In fact, many patients continue their current medications during TMS treatment. Your doctor will provide guidance on your specific treatment plan.",
    },
  ];

  return (
    <AppBar>
      <View style={styles.container}>
        <ScrollView
          style={styles.content}
          contentContainerStyle={{ paddingBottom: 70 }} // Add padding for bottom nav bar
        >
          {/* Hero Section */}
          <View style={[styles.heroSection, { height: isMobile ? dimensions.window.height * 0.6 : isTablet ? 500 : 600 }]}>
            <Image source={require("../../assets/hero-background.jpeg")} style={styles.heroBackground} />
            <View style={styles.heroOverlay}>
              <View style={[styles.heroContent, isMobile && { width: "100%" }]}>
                <Text style={[styles.heroTitle, styles.heroTitleMargin, isSmallDevice && { fontSize: 24 }]}>Discover Hope Without</Text>
                <Text style={[styles.heroTitle, isSmallDevice && { fontSize: 24 }]}>Medication</Text>

                <View style={styles.bbbBadge}>
                  <Image source={require("../../assets/bbb-badge.png")} style={styles.bbbImageLarge} resizeMode="contain" />
                </View>

                <Text style={[styles.heroDescription, isSmallDevice && { fontSize: 14 }]}>
                  If you're feeling overwhelmed and tired of the side effects of medication – TMS (Transcranial Magnetic
                  Stimulation) offers a safe, non-invasive, FDA approved option for treating depression. At TMS of Emerald
                  Coast, our experienced team is here to help you find relief.
                </Text>

                {/* Buttons Row */}
                <View style={styles.heroButtonsRow}>
                  <TouchableOpacity style={styles.contactButton} onPress={() => router.push("/contact")}>
                    <Text style={styles.contactButtonText}>Contact Us</Text>
                  </TouchableOpacity>
                  <TouchableOpacity style={styles.videoButton}>
                    <Ionicons name="play-outline" size={16} color="white" style={styles.videoIcon} />
                    <Text style={styles.videoButtonText}>Watch Video</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          </View>

          {/* Contact Form Section Below Hero - always visible */}
          <Pressable
            style={styles.heroFormSection}
            onPress={() => {
              setNameError("");
              setDateError("");
              setEmailError("");
              setConsultationTypeError("");
              Keyboard.dismiss();
            }}
          >
            <Text style={styles.heroFormTitle} numberOfLines={1} ellipsizeMode="tail">Get In Touch With TMS Of Emerald Coast</Text>
            <Text style={styles.heroFormTitleSecond}>Today</Text>
            {nameError ? (
              <View style={styles.heroFormErrorContainer}><Text style={styles.heroFormError}>{nameError}</Text></View>
            ) : null}
            <TextInput
              style={styles.heroFormInput}
              placeholder="Your Name"
              value={name}
              onChangeText={setName}
              placeholderTextColor="#bdbdbd"
              onFocus={() => setNameError("")}
            />
            {dateError ? (
              <View style={styles.heroFormErrorContainer}><Text style={styles.heroFormError}>{dateError}</Text></View>
            ) : null}
            <TextInput
              style={styles.heroFormInput}
              placeholder="Date-Month-Year"
              value={date}
              onChangeText={setDate}
              placeholderTextColor="#bdbdbd"
              onFocus={() => setDateError("")}
            />
            {emailError ? (
              <View style={styles.heroFormErrorContainer}><Text style={styles.heroFormError}>{emailError}</Text></View>
            ) : null}
            <TextInput
              style={styles.heroFormInput}
              placeholder="Your Email"
              value={email}
              onChangeText={setEmail}
              placeholderTextColor="#bdbdbd"
              keyboardType="email-address"
              onFocus={() => setEmailError("")}
            />
            {consultationTypeError ? (
              <View style={styles.heroFormErrorContainer}><Text style={styles.heroFormError}>{consultationTypeError}</Text></View>
            ) : null}
            <TouchableOpacity
              style={styles.heroFormDropdown}
              onPress={() => setShowConsultationOptions(!showConsultationOptions)}
              onPressIn={() => setConsultationTypeError("")}
            >
              <Text style={styles.heroFormDropdownText}>{consultationType}</Text>
              <Ionicons name={showConsultationOptions ? "chevron-up" : "chevron-down"} size={20} color="#333" style={{ marginLeft: 8 }} />
            </TouchableOpacity>
            {showConsultationOptions && (
              <View style={styles.heroFormDropdownOptions}>
                {['Consultation', 'Family Counseling', 'Anxiety Disorder', 'Depression', 'TMS Treatment'].map(option => (
                <TouchableOpacity
                    key={option}
                    style={styles.heroFormDropdownOption}
                  onPress={() => {
                      setConsultationType(option)
                    setShowConsultationOptions(false)
                  }}
                >
                    <Text style={styles.heroFormDropdownOptionText}>{option}</Text>
                </TouchableOpacity>
                ))}
              </View>
            )}
            <TouchableOpacity style={styles.heroFormButton} onPress={handleContactSubmit}>
              <Text style={styles.heroFormButtonText}>CONTACT US</Text>
            </TouchableOpacity>
          </Pressable>

          {/* TMS Info Section Below Form */}
          <View style={styles.tmsInfoSection}>
            <Text style={styles.tmsInfoHeading}>Be Part Of The 4 In 5 Patients Experiencing Relief With TMS.</Text>
            <Text style={styles.tmsInfoSubheading}>Embracing the healing power of magnetic fields</Text>
            <Text style={styles.tmsInfoText}>
              Transcranial Magnetic Stimulation (TMS) is a non-invasive procedure that uses magnetic fields to stimulate nerve cells in the brain. This innovative treatment has emerged as a promising solution for individuals struggling with various mental health conditions, particularly depression.
            </Text>
            <Text style={styles.tmsInfoText2}>
              <Text style={styles.tmsInfoSubheading2}>How Does it Work?</Text>{'\n'}
              TMS therapy works by using magnetic pulses to gently stimulate specific areas of the brain involved in mood regulation. These pulses help activate brain cells, which can improve symptoms of depression and other mental health conditions. The procedure is non-invasive, painless, and does not require medication.
            </Text>
            <View style={styles.tmsInfoButtonRow}>
              <TouchableOpacity style={styles.tmsInfoButton}>
                <Text style={styles.tmsInfoButtonText}>LEARN MORE</Text>
                <Ionicons name="arrow-forward" size={18} color={Colors.primary} style={styles.tmsInfoButtonIcon} />
              </TouchableOpacity>
            </View>
            <Image source={require("../../assets/patient-image.png")} style={styles.tmsInfoImage} resizeMode="cover" />
          </View>

          {/* Military TMS Section */}
          <View style={styles.militaryCardSection}>
            <View style={styles.militaryCard}>
              <Text style={styles.militaryCardHeading}>Transcranial Magnetic Stimulation TMS</Text>
              <Text style={styles.militaryCardText}>
                Therapy is increasingly being used to support military personnel, particularly in addressing conditions like Post-Traumatic Stress Disorder (PTSD), depression, and anxiety, which are common among veterans and active-duty members.
              </Text>
              <Image source={require("../../assets/tms-treatment.jpg")} style={styles.militaryCardImage} resizeMode="cover" />
            </View>

            <Text style={styles.militarySectionHeading}>How TMS Helps Military Personnel</Text>
            <Text style={styles.militarySectionSubheading}>PTSD Treatment</Text>
            <Text style={styles.militarySectionText}>
              TMS targets areas of the brain linked to emotional regulation and fear response, helping reduce PTSD symptoms such as flashbacks, hypervigilance, and mood instability.
            </Text>
            <Text style={styles.militarySectionSubheading}>Depression Relief</Text>
            <Text style={styles.militarySectionText}>
              Many military members experience treatment-resistant depression. TMS is an FDA-approved, non-invasive option that has shown effectiveness when traditional therapies fail.
            </Text>
            <Text style={styles.militarySectionSubheading}>Traumatic Brain Injury (TBI) Support</Text>
            <Text style={styles.militarySectionText}>
              Some research suggests TMS may help with cognitive function and mood regulation in those who have suffered mild TBI.
            </Text>
            <Text style={styles.militarySectionSubheading}>Anxiety & Insomnia</Text>
            <Text style={styles.militarySectionText}>
              TMS can regulate brain activity associated with anxiety disorders and sleep disturbances.
            </Text>
            <Text style={styles.militarySectionSubheading}>Non-Medicated Solution</Text>
            <Text style={styles.militarySectionText}>
              Many military personnel prefer non-drug treatments to avoid side effects and dependency issues associated with medications.
            </Text>

            <Text style={styles.militarySectionHeading2}>Why Military Personnel Benefit From TMS</Text>
            <View style={styles.militarySectionList}>
              <Text style={styles.militarySectionListItem}>• Non-invasive & drug-free</Text>
              <Text style={styles.militarySectionListItem}>• Minimal side effects (usually mild headaches or scalp discomfort)</Text>
              <Text style={styles.militarySectionListItem}>• Quick sessions (typically 20-40 minutes per session, 5 days a week for 4-6 weeks)</Text>
              <Text style={styles.militarySectionListItem}>• Long-term effect: Studies suggest benefits last for months after treatment.</Text>
            </View>

            <Text style={styles.militarySectionHeading2}>Availability & Military Coverage</Text>
            <View style={styles.militarySectionList}>
              <Text style={styles.militarySectionListItem}>• The U.S. Department of Veterans Affairs (VA) has been integrating TMS into PTSD and depression treatment programs.</Text>
              <Text style={styles.militarySectionListItem}>• TRICARE (military health insurance) and the VA may cover TMS therapy for qualifying service members and veterans.</Text>
              <Text style={styles.militarySectionListItem}>• Some private clinics also offer TMS specifically for veterans.</Text>
            </View>
          </View>

          {/* FAQ Section */}
          <View style={styles.faqSection}>
            <Text style={[styles.faqTitle, isSmallDevice && { fontSize: 28 }]}>Frequently Asked Questions</Text>

            {faqItems.map((item, index) => (
              <Pressable
                key={index}
                style={[styles.faqItem, expandedFaq === index && styles.faqItemExpanded]}
                onPress={() => toggleFaq(index)}
              >
                <View style={styles.faqQuestion}>
                  <Text style={[styles.faqQuestionText, isSmallDevice && { fontSize: 14 }]}>{item.question}</Text>
                  <Ionicons name={expandedFaq === index ? "chevron-up" : "chevron-down"} size={24} color={Colors.primary} />
                </View>
                {expandedFaq === index && (
                  <Text style={[styles.faqAnswerText, isSmallDevice && { fontSize: 14 }]}>{item.answer}</Text>
                )}
              </Pressable>
            ))}
          </View>

          {/* Take Control Section */}
          <View style={styles.takeControlSectionCustom}>
            <Text style={styles.takeControlTitleCustom}>Take Control Of</Text>
            <Text style={styles.takeControlSubtitleCustom}>Your Depression</Text>
            <Text style={styles.takeControlTextCustom}>
              Reclaim your life with TMS therapy. Schedule a consultation with our experienced team at TMS of Emerald Coast to discuss your treatment options and start your journey to recovery.
            </Text>
            <TouchableOpacity style={styles.takeControlButtonCustom} onPress={() => router.push("/contact")}>
              <Text style={styles.takeControlButtonTextCustom}>CONTACT US</Text>
              <Ionicons name="arrow-forward" size={18} color={Colors.primary} style={styles.takeControlButtonIconCustom} />
            </TouchableOpacity>
            <View style={styles.takeControlImageWrapper}>
              <Image source={require("../../assets/device.png")} style={styles.takeControlImageCustom} resizeMode="cover" />
            </View>
          </View>


        </ScrollView>
      </View>
    </AppBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  content: {
    flex: 1,
  },
  heroSection: {
    position: "relative",
    height: 600,
  },
  heroBackground: {
    position: "absolute",
    width: "100%",
    height: "100%",
  },
  heroOverlay: {
    backgroundColor: Colors.overlay,
    position: "absolute",
    width: "100%",
    height: "100%",
    flexDirection: "row",
    padding: Layout.spacing.large,
  },
  heroContent: {
    flex: 1,
    justifyContent: "center",
    width: "50%",
    marginTop: 24,
  },
  heroTitleMargin: {
    marginBottom: 0,
  },
  heroTitle: {
    color: Colors.white,
    fontSize: Fonts.sizes.xxlarge,
    fontWeight: Fonts.weights.bold,
    marginBottom: 0,
  },
  bbbBadge: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 0,
    paddingBottom: 0,
    marginTop: -16,
  },
  bbbImageLarge: {
    width: 160,
    height: 160,
    marginBottom: 0,
    paddingBottom: 0,
  },
  heroDescription: {
    color: Colors.white,
    fontSize: 16, // Increased from Fonts.sizes.regular (16) to 18
    lineHeight: 24, // Increased line height to match the new font size
    marginBottom: 30,
    marginTop: -32,
    paddingTop: 0,
  },
  heroButtonsRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 10,
  },
  contactButton: {
    backgroundColor: Colors.secondary,
    paddingVertical: 8,
    paddingHorizontal: 18,
    borderRadius: Layout.borderRadius.medium,
    alignSelf: 'flex-start',
    marginRight: 12,
  },
  contactButtonText: {
    color: Colors.white,
    fontSize: Fonts.sizes.medium,
    fontWeight: Fonts.weights.bold,
  },
  videoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.white,
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: Layout.borderRadius.medium,
    backgroundColor: Colors.transparent,
  },
  videoIcon: {
    marginRight: 6,
  },
  videoButtonText: {
    color: Colors.white,
    fontSize: Fonts.sizes.medium,
    fontWeight: Fonts.weights.bold,
  },
  heroFormSection: {
    backgroundColor: Colors.formBackground,
    borderRadius: Layout.borderRadius.xlarge,
    paddingVertical: 20,
    paddingHorizontal: 14,
    marginHorizontal: 24,
    marginTop: 32,
    marginBottom: 24,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 8,
  },
  heroFormTitle: {
    color: Colors.white,
    fontSize: 15.5,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 0,
    width: '100%',
    flexShrink: 1,
  },
  heroFormTitleSecond: {
    color: Colors.white,
    fontSize: 15.5,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 18,
    marginTop: 0,
  },
  heroFormInput: {
    backgroundColor: Colors.white,
    borderRadius: Layout.borderRadius.medium,
    paddingVertical: 14,
    paddingHorizontal: 12,
    fontSize: Fonts.sizes.medium,
    marginBottom: 18,
  },
  heroFormDropdown: {
    backgroundColor: Colors.white,
    borderRadius: Layout.borderRadius.medium,
    paddingVertical: 14,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 18,
  },
  heroFormDropdownText: {
    fontSize: Fonts.sizes.medium,
    color: '#222',
  },
  heroFormDropdownOptions: {
    backgroundColor: Colors.white,
    borderRadius: Layout.borderRadius.medium,
    marginBottom: 18,
    overflow: 'hidden',
  },
  heroFormDropdownOption: {
    paddingVertical: 14,
    paddingHorizontal: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray,
  },
  heroFormDropdownOptionText: {
    fontSize: Fonts.sizes.medium,
    color: '#222',
  },
  heroFormButton: {
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.medium,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 8,
  },
  heroFormButtonText: {
    color: Colors.white,
    fontSize: Fonts.sizes.medium,
    fontWeight: Fonts.weights.bold,
    letterSpacing: 1,
  },
  heroFormErrorContainer: {
    alignItems: 'flex-end',
    width: '100%',
  },
  heroFormError: {
    color: Colors.error,
    fontSize: 12,
    marginTop: 0,
    marginBottom: 6,
    marginRight: 2,
    textAlign: 'right',
  },
  tmsInfoSection: {
    backgroundColor: Colors.primary,
    borderRadius: 0,
    padding: 24,
    marginHorizontal: 0,
    marginBottom: 32,
  },
  tmsInfoHeading: {
    color: Colors.white,
    fontSize: Fonts.sizes.large,
    fontWeight: Fonts.weights.bold,
    marginBottom: 10,
    textAlign: 'left',
  },
  tmsInfoSubheading: {
    color: Colors.accent,
    fontSize: 15,
    fontWeight: Fonts.weights.bold,
    marginBottom: 10,
    textAlign: 'left',
  },
  tmsInfoSubheading2: {
    color: Colors.accent,
    fontSize: 15,
    fontWeight: Fonts.weights.bold,
    marginBottom: 10,
    textAlign: 'left',
  },
  tmsInfoText: {
    color: Colors.white,
    fontSize: Fonts.sizes.medium,
    marginBottom: 10,
    textAlign: 'left',
  },
  tmsInfoText2: {
    color: Colors.white,
    fontSize: Fonts.sizes.medium,
    marginBottom: 18,
    textAlign: 'left',
  },
  tmsInfoButtonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 18,
    flexWrap: 'nowrap',
    justifyContent: 'flex-start',
  },
  tmsInfoButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    borderRadius: Layout.borderRadius.medium,
    paddingVertical: 10,
    paddingHorizontal: 14,
    alignSelf: 'flex-start',
  },
  tmsInfoButtonText: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
    fontSize: 15,
    marginRight: 8,
  },
  tmsInfoButtonIcon: {
    marginLeft: 0,
  },
  tmsInfoImage: {
    width: '100%',
    height: 110,
    borderRadius: Layout.borderRadius.large,
    marginTop: 18,
  },
  militaryCardSection: {
    marginHorizontal: 0,
    marginBottom: 32,
    paddingHorizontal: 12,
  },
  militaryCard: {
    backgroundColor: Colors.primary,
    borderTopLeftRadius: Layout.borderRadius.xxlarge,
    borderTopRightRadius: Layout.borderRadius.xxlarge,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    padding: 20,
    marginHorizontal: 16,
    marginBottom: 18,
    alignItems: 'flex-start',
  },
  militaryCardHeading: {
    color: Colors.white,
    fontSize: Fonts.sizes.large,
    fontWeight: Fonts.weights.bold,
    marginBottom: 8,
  },
  militaryCardText: {
    color: Colors.white,
    fontSize: Fonts.sizes.medium,
    marginBottom: 12,
  },
  militaryCardImage: {
    width: '100%',
    height: 170,
    borderTopLeftRadius: Layout.borderRadius.large,
    borderTopRightRadius: Layout.borderRadius.large,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    marginTop: 8,
  },
  militarySectionHeading: {
    color: Colors.primary,
    fontSize: 17,
    fontWeight: Fonts.weights.bold,
    marginBottom: 8,
    marginLeft: 16,
    marginRight: 16,
    marginTop: 8,
  },
  militarySectionHeading2: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: Fonts.weights.bold,
    marginTop: 16,
    marginBottom: 8,
    marginLeft: 16,
  },
  militarySectionSubheading: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
    fontSize: 15,
    marginBottom: 2,
    marginLeft: 16,
    marginRight: 16,
    marginTop: 8,
  },
  militarySectionText: {
    color: Colors.text,
    fontSize: Fonts.sizes.medium,
    marginBottom: 2,
    marginLeft: 16,
    marginRight: 16,
  },
  militarySectionList: {
    marginLeft: 28,
    marginBottom: 8,
  },
  militarySectionListItem: {
    color: Colors.text,
    fontSize: Fonts.sizes.medium,
    marginBottom: 2,
  },
  faqSection: {
    padding: 40,
    backgroundColor: Colors.lightGray,
  },
  faqTitle: {
    fontSize: 28,
    fontWeight: Fonts.weights.bold,
    color: Colors.primary,
    marginBottom: 30,
    textAlign: "center",
  },
  faqItem: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 5,
    marginBottom: 15,
    overflow: "hidden",
  },
  faqItemExpanded: {
    borderColor: Colors.primary,
  },
  faqQuestion: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 8,
    paddingHorizontal: 15,
    backgroundColor: Colors.lightGray,
  },
  faqQuestionText: {
    fontSize: Fonts.sizes.regular,
    fontWeight: Fonts.weights.medium,
    color: Colors.primary,
    flex: 1,
  },
  faqAnswerText: {
    fontSize: Fonts.sizes.medium,
    lineHeight: 20,
    color: Colors.darkGray,
    padding: 15,
  },
  takeControlSectionCustom: {
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.xxlarge,
    padding: 24,
    marginHorizontal: 16,
    marginBottom: 32,
    alignItems: 'center',
  },
  takeControlTitleCustom: {
    color: Colors.white,
    fontSize: 24,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 0,
  },
  takeControlSubtitleCustom: {
    color: Colors.black,
    fontSize: 24,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 18,
    marginTop: 0,
  },
  takeControlTextCustom: {
    color: Colors.white,
    fontSize: 15,
    textAlign: 'center',
    marginBottom: 24,
    marginHorizontal: 8,
  },
  takeControlButtonCustom: {
    backgroundColor: Colors.white,
    borderRadius: Layout.borderRadius.xxlarge,
    paddingVertical: 12,
    paddingHorizontal: 32,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 24,
    marginTop: 0,
  },
  takeControlButtonTextCustom: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
    fontSize: 15,
    marginRight: 8,
  },
  takeControlButtonIconCustom: {
    marginLeft: 0,
  },
  takeControlImageWrapper: {
    width: '100%',
    backgroundColor: '#e5e7eb',
    borderRadius: Layout.borderRadius.large,
    alignItems: 'center',
    marginTop: 0,
  },
  takeControlImageCustom: {
    width: '100%',
    height: 110,
    borderRadius: Layout.borderRadius.large,
  },

});
