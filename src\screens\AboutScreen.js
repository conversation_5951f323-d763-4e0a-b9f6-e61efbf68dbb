import React from "react";
import { StyleSheet, View, Text, ScrollView, TouchableOpacity, SafeAreaView, Image, Linking } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter } from "expo-router";
import AppBar from "../components/AppBar";
import Colors from "../constants/Colors";
import Fonts from "../constants/Fonts";
import Layout from "../constants/Layout";

// Import context
import { useScrollViewPadding } from "../context/BottomNavContext";

/**
 * About Screen Component
 */
export default function AboutScreen() {
  const router = useRouter();
  const scrollViewPadding = useScrollViewPadding();

  const handlePlayVideo = () => {
    // Implement video playback functionality
    alert("Video playback would start here");
  };

  const handleContact = () => {
    router.push("/contact");
  };

  const teamMembers = [
    {
      name: "MINDY MCCLELLAN",
      role: "Director of Clinical Services, Certified Clinician",
      image: require("../../assets/expert1.png"),
    },
    {
      name: "CORNELIUS ALBERT",
      role: "Director of Marketing, Certified Clinician",
      image: require("../../assets/expert2.png"),
    },
    {
      name: "DR. VICTOR DE MOYA, MD",
      role: "Medical Director",
      image: require("../../assets/expert3.png"),
    },
    {
      name: "DR. JAMES IGLEBURGER, MD",
      role: "Medical Advisor",
      image: require("../../assets/expert4.png"),
    },
  ];

  return (
    <AppBar>
      <SafeAreaView style={styles.container}>
        <ScrollView contentContainerStyle={scrollViewPadding}>
          {/* Hero Section */}
          <View style={styles.heroSection}>
            <Image source={require("../../assets/about-hero.jpg")} style={styles.heroImage} />
            <View style={styles.heroOverlay}>
              <Text style={styles.heroTitle}>About Us</Text>
            </View>
          </View>

          {/* Video Section */}
          <View style={styles.videoSection}>
            <TouchableOpacity style={styles.videoContainer} onPress={handlePlayVideo}>
              <Image source={require("../../assets/video-thumbnail.png")} style={styles.videoThumbnail} />
              <View style={styles.playButton}>
                <Ionicons name="play" size={36} color={Colors.white} />
              </View>
            </TouchableOpacity>
          </View>

          {/* About Us Section */}
          <View style={styles.aboutSection}>
            <View style={styles.aboutHeaderRow}>
              <View style={styles.aboutHeaderLine} />
              <Text style={styles.aboutHeading}>ABOUT US</Text>
            </View>
            <Text style={styles.aboutTitle}>Dedicated To Finding{"\n"}Effective Solutions</Text>
            <Text style={styles.aboutText}>
              At TMS of Emerald Coast, we are dedicated to helping individuals achieve their highest level of happiness, recovery, and peace of mind. Through advanced Transcranial Magnetic Stimulation (TMS) protocols, our team of medical and clinical professionals is here to provide a safe, effective, and non-invasive treatment for those who have not responded to other forms of therapy or medication. We are deeply committed to offering genuine hope, innovative treatment, and a supportive environment.
            </Text>
            <Text style={styles.aboutText}>
              The reality is that depression and several other mental health conditions can be resistant to traditional treatments. We believe that everyone deserves a chance at recovery, and we are here to help you reclaim your life.
            </Text>
            <View style={styles.conditionsRow}>
              {["Depression", "Anxiety", "PTSD", "OCD"].map((item) => (
                <View key={item} style={styles.conditionItem}>
                  <View style={styles.checkCircle}>
                    <Ionicons name="checkmark" size={10} color="#fff" />
                  </View>
                  <Text style={styles.conditionText}>{item}</Text>
                </View>
              ))}
            </View>
            <TouchableOpacity style={styles.ctaButton} onPress={handleContact}>
              <Text style={styles.ctaButtonText}>CONTACT US</Text>
            </TouchableOpacity>
          </View>

          {/* Team Section */}
          <View style={styles.teamSection}>
            <View style={styles.teamHeaderRow}>
              <View style={styles.teamHeaderLine} />
              <Text style={styles.teamHeading}>OUR TEAM</Text>
              <View style={styles.teamHeaderLine} />
            </View>
            <Text style={styles.teamTitle}>
              Meet <Text style={styles.teamTitleBold}>Our</Text> Expert
            </Text>
            <View style={styles.teamGrid}>
              {teamMembers.map((member) => (
                <View key={member.name} style={styles.teamCard}>
                  <Image source={member.image} style={styles.teamImage} />
                  <Text style={styles.teamName}>{member.name}</Text>
                  <Text style={styles.teamRole}>{member.role}</Text>
                  <View style={styles.teamCardAccent} />
                </View>
              ))}
            </View>
          </View>

          {/* Call to Action Card */}
          <View style={styles.takeControlSectionCustom}>
            <Text style={styles.takeControlTitleCustom}>Take Control Of</Text>
            <Text style={styles.takeControlSubtitleCustom}>Your Depression</Text>
            <Text style={styles.takeControlTextCustom}>
              Reclaim your life with TMS therapy. Schedule a consultation with our experienced team at TMS of Emerald Coast to discuss your treatment options and start your journey to recovery.
            </Text>
            <TouchableOpacity style={styles.takeControlButtonCustom} onPress={handleContact}>
              <Text style={styles.takeControlButtonTextCustom}>CONTACT US</Text>
              <Ionicons name="arrow-forward" size={18} color={Colors.primary} style={styles.takeControlButtonIconCustom} />
            </TouchableOpacity>
            <View style={styles.takeControlImageWrapper}>
              <Image source={require("../../assets/depression.png")} style={styles.takeControlImageCustom} resizeMode="cover" />
            </View>
          </View>


        </ScrollView>
      </SafeAreaView>
    </AppBar>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  heroSection: {
    height: 220,
    position: "relative",
    marginBottom: Layout.spacing.large,
  },
  heroImage: {
    width: "100%",
    height: "100%",
  },
  heroOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: "rgba(44,82,100,0.25)",
    justifyContent: "center",
    alignItems: "flex-start",
    paddingLeft: Layout.spacing.xlarge,
    paddingTop: Layout.spacing.xxlarge,
  },
  heroTitle: {
    fontSize: Fonts.sizes.xxlarge,
    fontWeight: Fonts.weights.bold,
    color: Colors.white,
    letterSpacing: 1,
    textAlign: "left",
  },
  videoSection: {
    alignItems: "center",
    marginTop: Layout.spacing.large,
    marginBottom: Layout.spacing.large,
    zIndex: 2,
  },
  videoContainer: {
    width: 320,
    height: 180,
    borderRadius: Layout.borderRadius.large,
    overflow: "hidden",
    backgroundColor: Colors.gray,
    justifyContent: "center",
    alignItems: "center",
    elevation: 3,
  },
  videoThumbnail: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  playButton: {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: [{ translateX: -18 }, { translateY: -18 }],
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: "rgba(44,82,100,0.8)",
    justifyContent: "center",
    alignItems: "center",
  },
  aboutSection: {
    backgroundColor: Colors.white,
    marginHorizontal: Layout.spacing.large,
    marginTop: Layout.spacing.large,
    borderRadius: Layout.borderRadius.large,
    padding: Layout.spacing.xlarge,
    elevation: 2,
    shadowColor: Colors.black,
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  aboutHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 2,
  },
  aboutHeaderLine: {
    width: 36,
    height: 2,
    backgroundColor: Colors.primary,
    opacity: 0.4,
    marginRight: 6,
    borderRadius: 1,
  },
  aboutHeading: {
    color: Colors.primary,
    fontWeight: Fonts.weights.regular,
    fontSize: Fonts.sizes.large,
    letterSpacing: 1,
    textAlign: 'left',
  },
  aboutTitle: {
    fontSize: Fonts.sizes.xlarge,
    fontWeight: Fonts.weights.bold,
    color: Colors.primary,
    marginBottom: Layout.spacing.medium,
  },
  aboutText: {
    color: Colors.text,
    fontSize: Fonts.sizes.regular,
    marginBottom: Layout.spacing.small,
    lineHeight: 22,
  },
  conditionsRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "flex-start",
    marginVertical: Layout.spacing.large,
    flexWrap: "wrap",
    gap: 10,
  },
  conditionItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 10,
    marginBottom: 6,
  },
  checkCircle: {
    width: 15,
    height: 15,
    borderRadius: 7.5,
    backgroundColor: Colors.primary,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 4,
  },
  conditionText: {
    color: Colors.text,
    fontWeight: Fonts.weights.medium,
    fontSize: Fonts.sizes.regular,
  },
  ctaButton: {
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.medium,
    paddingVertical: 12,
    paddingHorizontal: 24,
    alignSelf: "center",
    marginTop: Layout.spacing.medium,
  },
  ctaButtonText: {
    color: Colors.white,
    fontWeight: Fonts.weights.bold,
    fontSize: Fonts.sizes.regular,
    letterSpacing: 1,
  },
  teamSection: {
    marginTop: Layout.spacing.xxlarge,
    marginBottom: Layout.spacing.xxlarge,
    paddingHorizontal: Layout.spacing.large,
    paddingTop: Layout.spacing.xxlarge,
    paddingBottom: Layout.spacing.xxlarge,
    backgroundColor: Colors.background,
  },
  teamHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  teamHeaderLine: {
    flex: 1,
    height: 2,
    backgroundColor: Colors.primary,
    opacity: 0.4,
    marginHorizontal: 8,
    borderRadius: 1,
  },
  teamHeading: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
    fontSize: Fonts.sizes.medium,
    letterSpacing: 2,
    textAlign: 'center',
    textTransform: 'uppercase',
  },
  teamTitle: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: Layout.spacing.xlarge,
    textAlign: 'center',
  },
  teamTitleBold: {
    color: Colors.black,
    fontWeight: '900',
  },
  teamGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 24,
  },
  teamCard: {
    backgroundColor: '#e6ece9',
    borderRadius: 16,
    alignItems: 'center',
    marginBottom: 16,
    padding: 0,
    width: '30%',
    minWidth: 150,
    maxWidth: 200,
    height: 212,
    flexGrow: 1,
    shadowColor: Colors.black,
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    elevation: 2,
    overflow: 'hidden',
  },
  teamImage: {
    width: '100%',
    height: 126.8,
    borderTopLeftRadius: 12,
    borderTopRightRadius: 12,
    borderBottomLeftRadius: 0,
    borderBottomRightRadius: 0,
    resizeMode: 'cover',
  },
  teamName: {
    fontWeight: '700',
    fontSize: 14,
    color: Colors.black,
    marginTop: 12,
    marginBottom: 2,
    textAlign: 'center',
    textTransform: 'uppercase',
    fontFamily: 'serif',
  },
  teamRole: {
    color: Colors.text,
    fontSize: Fonts.sizes.small,
    textAlign: 'center',
    opacity: 0.9,
    marginBottom: 12,
    marginTop: 2,
  },
  teamCardAccent: {
    height: 4,
    width: '100%',
    backgroundColor: Colors.primary,
    borderBottomLeftRadius: 16,
    borderBottomRightRadius: 16,
  },
  takeControlSectionCustom: {
    backgroundColor: Colors.primary,
    borderRadius: Layout.borderRadius.xxlarge,
    padding: 24,
    marginHorizontal: 16,
    marginBottom: 32,
    alignItems: 'center',
  },
  takeControlTitleCustom: {
    color: Colors.white,
    fontSize: 24,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 0,
  },
  takeControlSubtitleCustom: {
    color: Colors.black,
    fontSize: 24,
    fontWeight: Fonts.weights.bold,
    textAlign: 'center',
    marginBottom: 18,
    marginTop: 0,
  },
  takeControlTextCustom: {
    color: Colors.white,
    fontSize: 15,
    textAlign: 'center',
    marginBottom: 24,
    marginHorizontal: 8,
  },
  takeControlButtonCustom: {
    backgroundColor: Colors.white,
    borderRadius: Layout.borderRadius.xxlarge,
    paddingVertical: 12,
    paddingHorizontal: 32,
    flexDirection: 'row',
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 24,
    marginTop: 0,
  },
  takeControlButtonTextCustom: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
    fontSize: 15,
    marginRight: 8,
  },
  takeControlButtonIconCustom: {
    marginLeft: 0,
  },
  takeControlImageWrapper: {
    width: '100%',
    backgroundColor: '#e5e7eb',
    borderRadius: Layout.borderRadius.large,
    alignItems: 'center',
    marginTop: 0,
  },
  takeControlImageCustom: {
    width: '100%',
    height: 110,
    borderRadius: Layout.borderRadius.large,
  },

});


