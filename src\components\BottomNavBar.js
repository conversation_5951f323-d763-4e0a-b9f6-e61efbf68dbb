import React from "react";
import { StyleSheet, View, Text, TouchableOpacity } from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { useRouter, usePathname } from "expo-router";

// Import constants
import Colors from "../constants/Colors";
import Fonts from "../constants/Fonts";
import Layout from "../constants/Layout";

/**
 * Bottom Navigation Bar Component
 */
export default function BottomNavBar() {
  const router = useRouter();
  const pathname = usePathname();

  // Navigation items with their icons and routes
  const navItems = [
    {
      label: "Home",
      icon: "home",
      route: "/",
      activeIcon: "home",
    },
    {
      label: "About",
      icon: "information-circle-outline",
      route: "/about",
      activeIcon: "information-circle",
    },
    {
      label: "Treatment",
      icon: "medical-outline",
      route: "/treatment",
      activeIcon: "medical",
    },
    {
      label: "New Patients",
      icon: "person-add-outline",
      route: "/new-patients",
      activeIcon: "person-add",
    },
    {
      label: "Contact",
      icon: "call-outline",
      route: "/contact",
      activeIcon: "call",
    },
  ];

  // Check if a route is active
  const isActive = (route) => {
    if (route === "/" && pathname === "/") return true;
    if (route !== "/" && pathname.startsWith(route)) return true;
    return false;
  };

  return (
    <View style={styles.container}>
      {navItems.map((item) => (
        <TouchableOpacity
          key={item.label}
          style={styles.navItem}
          onPress={() => router.push(item.route)}
          activeOpacity={0.7}
        >
          <Ionicons
            name={isActive(item.route) ? item.activeIcon : item.icon}
            size={24}
            color={isActive(item.route) ? Colors.primary : Colors.lightText}
          />
          <Text
            style={[
              styles.navLabel,
              isActive(item.route) && styles.activeNavLabel,
            ]}
          >
            {item.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: "#eee",
    paddingVertical: 8,
    paddingHorizontal: 5,
    justifyContent: "space-between",
    elevation: 10,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: -3 },
    shadowOpacity: 0.1,
    shadowRadius: 5,
  },
  navItem: {
    flex: 1,
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: 8,
  },
  navLabel: {
    fontSize: Layout.isSmallDevice ? 10 : 12,
    marginTop: 4,
    color: Colors.lightText,
    textAlign: "center",
  },
  activeNavLabel: {
    color: Colors.primary,
    fontWeight: Fonts.weights.bold,
  },
});
