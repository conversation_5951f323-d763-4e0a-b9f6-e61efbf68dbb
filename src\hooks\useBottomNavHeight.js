import { useState, useEffect } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import Layout from '../constants/Layout';

/**
 * Custom hook to calculate the dynamic bottom navigation height
 * Takes into account device size, safe area insets, and actual component dimensions
 */
export const useBottomNavHeight = () => {
  const insets = useSafeAreaInsets();
  const [bottomNavHeight, setBottomNavHeight] = useState(70); // Default fallback

  useEffect(() => {
    // Calculate bottom nav height based on:
    // - Icon size (24px)
    // - Label font size (10px for small devices, 12px for normal)
    // - Padding vertical (8px top + 8px bottom = 16px)
    // - Label margin top (4px)
    // - Border top width (1px)
    // - Safe area bottom inset
    
    const iconSize = 24;
    const labelFontSize = Layout.isSmallDevice ? 10 : 12;
    const paddingVertical = 16; // 8px top + 8px bottom
    const labelMarginTop = 4;
    const borderTopWidth = 1;
    
    // Calculate the content height
    const contentHeight = iconSize + labelMarginTop + labelFontSize + paddingVertical + borderTopWidth;
    
    // Add safe area bottom inset for devices with home indicator
    const totalHeight = contentHeight + insets.bottom;
    
    setBottomNavHeight(totalHeight);
  }, [insets.bottom]);

  return bottomNavHeight;
};

/**
 * Hook to get the safe padding bottom for ScrollView content
 * This ensures content doesn't get hidden behind the bottom navigation
 */
export const useScrollViewPadding = () => {
  const bottomNavHeight = useBottomNavHeight();
  
  // Add extra padding to ensure content is fully visible
  const paddingBottom = bottomNavHeight + 10; // 10px extra buffer
  
  return { paddingBottom };
};
