import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { SafeAreaProvider } from "react-native-safe-area-context";
import Colors from "../src/constants/Colors";
import { BottomNavProvider } from "../src/context/BottomNavContext";

/**
 * Root layout for the app
 */
export default function RootLayout() {
  return (
    <SafeAreaProvider>
      <BottomNavProvider>
        <StatusBar style="light" />
        <Stack
          screenOptions={{
            headerShown: false,
            contentStyle: { backgroundColor: Colors.background },
            animation: 'slide_from_right',
            animationDuration: 300,
            gestureEnabled: true,
            gestureDirection: 'horizontal',
          }}
        />
      </BottomNavProvider>
    </SafeAreaProvider>
  );
}
